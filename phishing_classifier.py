# phishing_classifier.py
import pandas as pd
import numpy as np
import joblib
import tldextract
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse
import re
import warnings
warnings.filterwarnings('ignore')

# Load saved model and preprocessor
model = joblib.load('final_stacking_model.joblib')
preprocessor = joblib.load('features_preprocessor.joblib')

def extract_features(url):
    """Extract 30 features from a raw URL"""
    features = {}
    
    try:
        # Basic URL features
        parsed = urlparse(url)
        ext = tldextract.extract(url)
        
        # Length-based features
        features['URLLength'] = len(url)
        features['NoOfLettersInURL'] = sum(c.isalpha() for c in url)
        features['LetterRatioInURL'] = features['NoOfLettersInURL'] / max(1, features['URLLength'])
        features['NoOfDegitsInURL'] = sum(c.isdigit() for c in url)
        features['NoOfOtherSpecialCharsInURL'] = sum(not c.isalnum() for c in url)
        features['CharContinuationRate'] = 1 - (url.count('..') / max(1, features['URLLength']))
        
        # Domain features
        features['TLD'] = ext.suffix.lower()
        features['TLDLegitimateProb'] = 0.9  # Default value (would need real data for actual score)
        features['URLSimilarityIndex'] = 0.5  # Placeholder
        
        # Protocol features
        features['IsHTTPS'] = 1 if parsed.scheme.lower() == 'https' else 0
        
        # Fetch HTML content for advanced features
        try:
            response = requests.get(url, timeout=10, headers={'User-Agent': 'PhishingClassifier/1.0'})
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Content-based features
            features['LineOfCode'] = len(soup.find_all())
            features['LargestLineLength'] = max((len(str(tag)) for tag in soup.find_all()), default=0)
            features['HasTitle'] = 1 if soup.title else 0
            features['Title'] = str(soup.title.string) if soup.title else 'no_title'
            features['DomainTitleMatchScore'] = 0.7  # Placeholder
            features['URLTitleMatchScore'] = 0.6  # Placeholder
            features['HasFavicon'] = 1 if soup.find('link', rel=['icon', 'shortcut icon']) else 0
            features['IsResponsive'] = 1 if soup.find(media='(max-width: 600px)') else 0
            features['HasDescription'] = 1 if soup.find('meta', attrs={'name': 'description'}) else 0
            features['NoOfiFrame'] = len(soup.find_all('iframe'))
            features['HasSocialNet'] = 1 if re.search(r'(facebook|twitter|instagram)', response.text, re.I) else 0
            features['HasSubmitButton'] = 1 if soup.find('input', {'type': 'submit'}) else 0
            features['HasHiddenFields'] = len(soup.find_all('input', {'type': 'hidden'}))
            features['HasCopyrightInfo'] = 1 if re.search(r'copyright', response.text, re.I) else 0
            features['NoOfImage'] = len(soup.find_all('img'))
            features['NoOfCSS'] = len(soup.find_all('link', rel='stylesheet'))
            features['NoOfJS'] = len(soup.find_all('script'))
            
            # Link analysis
            links = [a.get('href') for a in soup.find_all('a', href=True)]
            features['NoOfSelfRef'] = sum(1 for l in links if parsed.netloc in l)
            features['NoOfEmptyRef'] = sum(1 for l in links if l == '#')
            features['NoOfExternalRef'] = sum(1 for l in links if parsed.netloc not in l)
            
        except Exception as e:
            # Default values for failed requests
            print(f"⚠️ Failed to fetch content: {str(e)}")
            features.update({
                'LineOfCode': 0,
                'LargestLineLength': 0,
                'HasTitle': 0,
                'Title': 'fetch_error',
                'DomainTitleMatchScore': 0,
                'URLTitleMatchScore': 0,
                'HasFavicon': 0,
                'IsResponsive': 0,
                'HasDescription': 0,
                'NoOfiFrame': 0,
                'HasSocialNet': 0,
                'HasSubmitButton': 0,
                'HasHiddenFields': 0,
                'HasCopyrightInfo': 0,
                'NoOfImage': 0,
                'NoOfCSS': 0,
                'NoOfJS': 0,
                'NoOfSelfRef': 0,
                'NoOfEmptyRef': 0,
                'NoOfExternalRef': 0
            })
            
        return pd.DataFrame([features])
    
    except Exception as e:
        print(f"❌ Error extracting features: {str(e)}")
        return None

def predict_phishing(url):
    """Predict phishing status for a given URL"""
    print(f"\n🔍 Analyzing URL: {url}")
    
    # Extract features
    df = extract_features(url)
    if df is None or df.empty:
        return None
    
    # Apply preprocessing
    try:
        processed = preprocessor.transform(df)
        # Make prediction
        prediction = model.predict(processed)[0]
        probability = model.predict_proba(processed)[0][1]
        
        return {
            'prediction': 'Phishing' if prediction == 1 else 'Legitimate',
            'probability': probability,
            'phishing_score': probability,
            'confidence': max(probability, 1-probability)
        }
        
    except Exception as e:
        print(f"❌ Processing error: {str(e)}")
        return None

if __name__ == "__main__":
    print("🛡️ Phishing URL Classifier")
    print("=" * 30)
    
    while True:
        url = input("\nEnter URL (or 'q' to quit): ").strip()
        if url.lower() in ['q', 'quit', 'exit']:
            break
            
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
            
        result = predict_phishing(url)
        if result:
            print(f"\n🎯 Prediction: {result['prediction']}")
            print(f"📊 Phishing Probability: {result['probability']:.4f}")
            print(f"✅ Confidence: {result['confidence']:.2%}")